{"__meta": {"id": "01JZXRCVFNMDK92RZ25BP3JD9K", "datetime": "2025-07-11 22:06:20", "utime": **********.661931, "method": "POST", "uri": "/install/setup", "ip": "::1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752271579.467978, "end": **********.661944, "duration": 1.1939659118652344, "duration_str": "1.19s", "measures": [{"label": "Booting", "start": 1752271579.467978, "relative_start": 0, "end": **********.063379, "relative_end": **********.063379, "duration": 0.****************, "duration_str": "595ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.063391, "relative_start": 0.****************, "end": **********.661946, "relative_end": 2.1457672119140625e-06, "duration": 0.****************, "duration_str": "599ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.077049, "relative_start": 0.****************, "end": **********.080271, "relative_end": **********.080271, "duration": 0.0032219886779785156, "duration_str": "3.22ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.659309, "relative_start": 1.***************, "end": **********.659754, "relative_end": **********.659754, "duration": 0.0004451274871826172, "duration_str": "445μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "36MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.28", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost/whats", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 10, "nb_statements": 8, "nb_visible_statements": 10, "nb_excluded_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.08001000000000001, "accumulated_duration_str": "80.01ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 8, "namespace": null, "name": "vendor/corbital/installer/src/Http/Controllers/InstallController.php", "file": "C:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Http\\Controllers\\InstallController.php", "line": 258}, {"index": 9, "namespace": null, "name": "vendor/corbital/installer/src/Http/Controllers/InstallController.php", "file": "C:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Http\\Controllers\\InstallController.php", "line": 88}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.103273, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "InstallController.php:258", "source": {"index": 8, "namespace": null, "name": "vendor/corbital/installer/src/Http/Controllers/InstallController.php", "file": "C:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Http\\Controllers\\InstallController.php", "line": 258}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fcorbital%2Finstaller%2Fsrc%2FHttp%2FControllers%2FInstallController.php&line=258", "ajax": false, "filename": "InstallController.php", "line": "258"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `test_table`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/corbital/installer/src/Classes/DatabaseTest.php", "file": "C:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Classes\\DatabaseTest.php", "line": 105}, {"index": 16, "namespace": null, "name": "vendor/corbital/installer/src/Classes/DatabaseTest.php", "file": "C:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Classes\\DatabaseTest.php", "line": 104}, {"index": 17, "namespace": null, "name": "vendor/corbital/installer/src/Classes/DatabaseTest.php", "file": "C:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Classes\\DatabaseTest.php", "line": 52}, {"index": 18, "namespace": null, "name": "vendor/corbital/installer/src/Classes/DatabaseTest.php", "file": "C:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Classes\\DatabaseTest.php", "line": 100}, {"index": 19, "namespace": null, "name": "vendor/corbital/installer/src/Classes/DatabaseTest.php", "file": "C:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Classes\\DatabaseTest.php", "line": 202}], "start": **********.301003, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "DatabaseTest.php:105", "source": {"index": 13, "namespace": null, "name": "vendor/corbital/installer/src/Classes/DatabaseTest.php", "file": "C:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Classes\\DatabaseTest.php", "line": 105}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fcorbital%2Finstaller%2Fsrc%2FClasses%2FDatabaseTest.php&line=105", "ajax": false, "filename": "DatabaseTest.php", "line": "105"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 0, "width_percent": 1.137}, {"sql": "update `test_table` set `test_column` = 'Corbital Updated'", "type": "query", "params": [], "bindings": ["Corbital Updated"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/corbital/installer/src/Classes/DatabaseTest.php", "file": "C:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Classes\\DatabaseTest.php", "line": 120}, {"index": 14, "namespace": null, "name": "vendor/corbital/installer/src/Classes/DatabaseTest.php", "file": "C:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Classes\\DatabaseTest.php", "line": 119}, {"index": 15, "namespace": null, "name": "vendor/corbital/installer/src/Classes/DatabaseTest.php", "file": "C:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Classes\\DatabaseTest.php", "line": 52}, {"index": 16, "namespace": null, "name": "vendor/corbital/installer/src/Classes/DatabaseTest.php", "file": "C:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Classes\\DatabaseTest.php", "line": 115}, {"index": 17, "namespace": null, "name": "vendor/corbital/installer/src/Classes/DatabaseTest.php", "file": "C:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Classes\\DatabaseTest.php", "line": 203}], "start": **********.3305311, "duration": 0.00186, "duration_str": "1.86ms", "memory": 0, "memory_str": null, "filename": "DatabaseTest.php:120", "source": {"index": 11, "namespace": null, "name": "vendor/corbital/installer/src/Classes/DatabaseTest.php", "file": "C:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Classes\\DatabaseTest.php", "line": 120}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fcorbital%2Finstaller%2Fsrc%2FClasses%2FDatabaseTest.php&line=120", "ajax": false, "filename": "DatabaseTest.php", "line": "120"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 1.137, "width_percent": 2.325}, {"sql": "delete from `test_table`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/corbital/installer/src/Classes/DatabaseTest.php", "file": "C:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Classes\\DatabaseTest.php", "line": 135}, {"index": 14, "namespace": null, "name": "vendor/corbital/installer/src/Classes/DatabaseTest.php", "file": "C:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Classes\\DatabaseTest.php", "line": 134}, {"index": 15, "namespace": null, "name": "vendor/corbital/installer/src/Classes/DatabaseTest.php", "file": "C:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Classes\\DatabaseTest.php", "line": 52}, {"index": 16, "namespace": null, "name": "vendor/corbital/installer/src/Classes/DatabaseTest.php", "file": "C:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Classes\\DatabaseTest.php", "line": 130}, {"index": 17, "namespace": null, "name": "vendor/corbital/installer/src/Classes/DatabaseTest.php", "file": "C:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Classes\\DatabaseTest.php", "line": 204}], "start": **********.360766, "duration": 0.0017800000000000001, "duration_str": "1.78ms", "memory": 0, "memory_str": null, "filename": "DatabaseTest.php:135", "source": {"index": 11, "namespace": null, "name": "vendor/corbital/installer/src/Classes/DatabaseTest.php", "file": "C:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Classes\\DatabaseTest.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fcorbital%2Finstaller%2Fsrc%2FClasses%2FDatabaseTest.php&line=135", "ajax": false, "filename": "DatabaseTest.php", "line": "135"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 3.462, "width_percent": 2.225}, {"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "vendor/corbital/installer/src/Http/Controllers/InstallController.php", "file": "C:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Http\\Controllers\\InstallController.php", "line": 115}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.563268, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "InstallController.php:115", "source": {"index": 9, "namespace": null, "name": "vendor/corbital/installer/src/Http/Controllers/InstallController.php", "file": "C:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Http\\Controllers\\InstallController.php", "line": 115}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fcorbital%2Finstaller%2Fsrc%2FHttp%2FControllers%2FInstallController.php&line=115", "ajax": false, "filename": "InstallController.php", "line": "115"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 5.687, "width_percent": 0}, {"sql": "SET FOREIGN_KEY_CHECKS=0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/corbital/installer/src/Http/Controllers/InstallController.php", "file": "C:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Http\\Controllers\\InstallController.php", "line": 116}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.563365, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "InstallController.php:116", "source": {"index": 11, "namespace": null, "name": "vendor/corbital/installer/src/Http/Controllers/InstallController.php", "file": "C:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Http\\Controllers\\InstallController.php", "line": 116}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fcorbital%2Finstaller%2Fsrc%2FHttp%2FControllers%2FInstallController.php&line=116", "ajax": false, "filename": "InstallController.php", "line": "116"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 5.687, "width_percent": 2.187}, {"sql": "select exists (select 1 from information_schema.tables where table_schema = 'whatsmark_non_saas' and table_name = 'sessions' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/corbital/installer/src/Http/Controllers/InstallController.php", "file": "C:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Http\\Controllers\\InstallController.php", "line": 118}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.570218, "duration": 0.00435, "duration_str": "4.35ms", "memory": 0, "memory_str": null, "filename": "InstallController.php:118", "source": {"index": 13, "namespace": null, "name": "vendor/corbital/installer/src/Http/Controllers/InstallController.php", "file": "C:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Http\\Controllers\\InstallController.php", "line": 118}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fcorbital%2Finstaller%2Fsrc%2FHttp%2FControllers%2FInstallController.php&line=118", "ajax": false, "filename": "InstallController.php", "line": "118"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 7.874, "width_percent": 5.437}, {"sql": "create table `sessions` (`id` varchar(255) not null, `user_id` bigint unsigned null, `ip_address` varchar(45) null, `user_agent` text null, `payload` longtext not null, `last_activity` int not null, primary key (`id`)) default character set utf8mb4 collate 'utf8mb4_unicode_ci' engine = InnoDB", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/corbital/installer/src/Http/Controllers/InstallController.php", "file": "C:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Http\\Controllers\\InstallController.php", "line": 119}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.5796769, "duration": 0.016730000000000002, "duration_str": "16.73ms", "memory": 0, "memory_str": null, "filename": "InstallController.php:119", "source": {"index": 13, "namespace": null, "name": "vendor/corbital/installer/src/Http/Controllers/InstallController.php", "file": "C:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Http\\Controllers\\InstallController.php", "line": 119}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fcorbital%2Finstaller%2Fsrc%2FHttp%2FControllers%2FInstallController.php&line=119", "ajax": false, "filename": "InstallController.php", "line": "119"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 13.311, "width_percent": 20.91}, {"sql": "alter table `sessions` add index `sessions_user_id_index`(`user_id`)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/corbital/installer/src/Http/Controllers/InstallController.php", "file": "C:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Http\\Controllers\\InstallController.php", "line": 119}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.5981932, "duration": 0.03804, "duration_str": "38.04ms", "memory": 0, "memory_str": null, "filename": "InstallController.php:119", "source": {"index": 13, "namespace": null, "name": "vendor/corbital/installer/src/Http/Controllers/InstallController.php", "file": "C:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Http\\Controllers\\InstallController.php", "line": 119}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fcorbital%2Finstaller%2Fsrc%2FHttp%2FControllers%2FInstallController.php&line=119", "ajax": false, "filename": "InstallController.php", "line": "119"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 34.221, "width_percent": 47.544}, {"sql": "alter table `sessions` add index `sessions_last_activity_index`(`last_activity`)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/corbital/installer/src/Http/Controllers/InstallController.php", "file": "C:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Http\\Controllers\\InstallController.php", "line": 119}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.637786, "duration": 0.01459, "duration_str": "14.59ms", "memory": 0, "memory_str": null, "filename": "InstallController.php:119", "source": {"index": 13, "namespace": null, "name": "vendor/corbital/installer/src/Http/Controllers/InstallController.php", "file": "C:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Http\\Controllers\\InstallController.php", "line": 119}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fcorbital%2Finstaller%2Fsrc%2FHttp%2FControllers%2FInstallController.php&line=119", "ajax": false, "filename": "InstallController.php", "line": "119"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 81.765, "width_percent": 18.235}]}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "302 Found", "full_url": "http://localhost:8000/install/setup", "action_name": "install.setup.store", "controller_action": "Corbital\\Installer\\Http\\Controllers\\InstallController@setupStore", "uri": "POST install/setup", "controller": "Corbital\\Installer\\Http\\Controllers\\InstallController@setupStore<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fcorbital%2Finstaller%2Fsrc%2FHttp%2FControllers%2FInstallController.php&line=84\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "install", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fcorbital%2Finstaller%2Fsrc%2FHttp%2FControllers%2FInstallController.php&line=84\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/corbital/installer/src/Http/Controllers/InstallController.php:84-159</a>", "middleware": "web, Corbital\\Installer\\Http\\Middleware\\CanInstall", "duration": "1.22s", "peak_memory": "36MB", "response": "Redirect to http://localhost:8000/install/license", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-117213642 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">40QMbx4uEb2Re2xoM5y7QokpCbUALVcgTm54X2N3</span>\"\n  \"<span class=sf-dump-key>app_url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  \"<span class=sf-dump-key>app_name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Laravel</span>\"\n  \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str title=\"3 characters\">105</span>\"\n  \"<span class=sf-dump-key>database_hostname</span>\" => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  \"<span class=sf-dump-key>database_port</span>\" => \"<span class=sf-dump-str title=\"4 characters\">3306</span>\"\n  \"<span class=sf-dump-key>database_name</span>\" => \"<span class=sf-dump-str title=\"18 characters\">whatsmark_non_saas</span>\"\n  \"<span class=sf-dump-key>database_username</span>\" => \"<span class=sf-dump-str title=\"4 characters\">root</span>\"\n  \"<span class=sf-dump-key>database_password</span>\" => \"<span class=sf-dump-str title=\"4 characters\">root</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-117213642\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1771002029 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">240</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://localhost:8000/install/setup</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkthaFplaEkrbDdYaDlpdUdvMmNPc3c9PSIsInZhbHVlIjoidjN0VjhwN2RpNjVjaENzVVo5SlRzVHg0VUN6QldsTXBFNmxETmJ1WDBhRDNmVGJmVmM5Q3BqWnpxeHVuSkI5WE1qVEo5eitKUVpXNnFhRnN5RHhQY0V4cHUyNFNQN2FUT3hPa2lEb1R3enluTWZzVnpBNDVlSzNqbEp0NW9aYmkiLCJtYWMiOiI2MTkzNzliNWE0NTA4MjYxZmU1MDUyNDhiZWY0ZGRkYmZmYWM4MWU2YTM0MzllZDBlN2E5YjM5MmFmMTEwZTM5IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjYwRFFGMEZISzViWlZFSUUza3NlSnc9PSIsInZhbHVlIjoieW9HL3JoVWpGdXZyL0JySWE1SjA0YTgwN0p6TXFTR3kyRmN6TVlORjI5UGJVTFhpRHNtcllhN1AwaU5xc2kvNm9kOHZQSzI1ZS9tUno5M2dOcWcycjhTNWZCUUV0YnJEdzNzZE1PKzhVMEpUSXZPZFBkZisyME5qb0VmUWIvN0wiLCJtYWMiOiI4MDJkMjc3NTI5Nzk2MWNlMTcyMWY4NjRiZmIzNTA0ZTUxNTRiZmZjYmZmYzQzNjZjZThmNDMyNjc4Njg2OTczIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1771002029\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-339001003 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">40QMbx4uEb2Re2xoM5y7QokpCbUALVcgTm54X2N3</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">27pIBGGbiZrra0WKoTBCImejjaWqpombXfF8Wtf5</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-339001003\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 11 Jul 2025 22:06:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost:8000/install/license</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-310641615 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">40QMbx4uEb2Re2xoM5y7QokpCbUALVcgTm54X2N3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost:8000/install</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str title=\"3 characters\">105</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-310641615\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "http://localhost:8000/install/setup", "action_name": "install.setup.store", "controller_action": "Corbital\\Installer\\Http\\Controllers\\InstallController@setupStore"}, "badge": "302 Found"}}