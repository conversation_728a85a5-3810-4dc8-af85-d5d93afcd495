<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckDatabaseVersion
{
    /**
     * Handle an incoming request.
     *
     * @param \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response) $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if ($request->is('database-upgrade')) {
            return $this->databaseNeedsUpgrade()
                ? $next($request)
                : redirect()->route('admin.dashboard');
        }

        if ($request->is('upgrade')) {
            return $next($request);
        }

        if ($this->databaseNeedsUpgrade()) {
            return redirect()->route('database.upgrade');
        }

        return $next($request);

    }

    /**
     * Determine if the database needs an upgrade
     */
    protected function databaseNeedsUpgrade(): bool
    {
        try {
            $currentVersion = config('installer.license_verification.current_version'); // Your actual app version

            // Get database version
            // This is a placeholder - implement according to your version tracking system
            $databaseVersion = get_setting('whats-mark.wm_version'); // Replace with actual version retrieval

            // Compare versions using semantic versioning
            return version_compare($currentVersion, $databaseVersion, '>');
        } catch (\Exception $e) {
            whatsapp_log('Error checking database version', 'error', [
                'error' => $e->getMessage(),
            ], $e);

            // If we can't check, assume no upgrade is needed
            return false;
        }
    }
}
