{"__meta": {"id": "01JZXRBZYCJWF56782W6R1WQ99", "datetime": "2025-07-11 22:05:52", "utime": **********.462723, "method": "GET", "uri": "/install", "ip": "::1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.091033, "end": **********.462746, "duration": 0.37171292304992676, "duration_str": "372ms", "measures": [{"label": "Booting", "start": **********.091033, "relative_start": 0, "end": **********.43273, "relative_end": **********.43273, "duration": 0.*****************, "duration_str": "342ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.432739, "relative_start": 0.****************, "end": **********.462748, "relative_end": 2.1457672119140625e-06, "duration": 0.030009031295776367, "duration_str": "30.01ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.441765, "relative_start": 0.*****************, "end": **********.445558, "relative_end": **********.445558, "duration": 0.003793001174926758, "duration_str": "3.79ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.452385, "relative_start": 0.*****************, "end": **********.457459, "relative_end": **********.457459, "duration": 0.005074024200439453, "duration_str": "5.07ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: installer::installation.welcome", "start": **********.45445, "relative_start": 0.****************, "end": **********.45445, "relative_end": **********.45445, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: installer::installation.layout", "start": **********.456393, "relative_start": 0.****************, "end": **********.456393, "relative_end": **********.456393, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 31774040, "peak_usage_str": "30MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.28", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost/whats", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 2, "nb_templates": 2, "templates": [{"name": "installer::installation.welcome", "param_count": null, "params": [], "start": **********.454409, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Providers/../resources/views/installation/welcome.blade.phpinstaller::installation.welcome", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fcorbital%2Finstaller%2Fsrc%2Fresources%2Fviews%2Finstallation%2Fwelcome.blade.php&line=1", "ajax": false, "filename": "welcome.blade.php", "line": "?"}}, {"name": "installer::installation.layout", "param_count": null, "params": [], "start": **********.456359, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Providers/../resources/views/installation/layout.blade.phpinstaller::installation.layout", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fcorbital%2Finstaller%2Fsrc%2Fresources%2Fviews%2Finstallation%2Flayout.blade.php&line=1", "ajax": false, "filename": "layout.blade.php", "line": "?"}}]}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/install", "action_name": "install.index", "controller_action": "Corbital\\Installer\\Http\\Controllers\\InstallController@index", "uri": "GET install", "controller": "Corbital\\Installer\\Http\\Controllers\\InstallController@index<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fcorbital%2Finstaller%2Fsrc%2FHttp%2FControllers%2FInstallController.php&line=30\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "install", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fcorbital%2Finstaller%2Fsrc%2FHttp%2FControllers%2FInstallController.php&line=30\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/corbital/installer/src/Http/Controllers/InstallController.php:30-33</a>", "middleware": "web, Corbital\\Installer\\Http\\Middleware\\CanInstall", "duration": "372ms", "peak_memory": "32MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1406923351 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1406923351\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-561995563 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-561995563\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-71058528 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">text/css,*/*;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">style</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://localhost:8000/install/setup</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InRQRm9tekl0ZG5IcUVFemhTcWRmQkE9PSIsInZhbHVlIjoibmtxbzZ3QzlnTDZQTmYyd2RJZ3pFYkx5TVhNSWhGM1dBNUszdEdMKzBtbDZmVU9kaEtpNnV5di9Kb3o5MGdjQjkzdFFtNm1hVS9iNVo1OFkyZUFPaU1mZlhsclh2anRyVEhseGlxWTNBcS9nU2ZsTzBvM1kzcVNKV2RjT09lNVEiLCJtYWMiOiJiZjE5ZjdhNGEwMzY5MDA2YzA1OGFkMzQ4ZjUxNWRiNDEwMWY1MjMyNDNlMDgwMjc3OGQ0N2QzNmJmNjk0YzhkIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkY4ZVN1WkdRRUswMXN5czhCMjFNSXc9PSIsInZhbHVlIjoiQlpUU0RudEhySXNubC9lTldmQUdjOWdEZW9LT1hzeUVQSVl6ajRFelQzQkR3UVdOTDRFUTlPRjU1YVZzaU5oektIWWt4dXZmUE9KdGI0R3pNTVRoNmp2Z3VpczJlWkVTTzR3T2RTZUZRbm9EYndlZFhPU1Q4MnFsREhWYm1OZDYiLCJtYWMiOiJiNTgzYTU0Nzg5ZDAyOWY4YThlZThlZDJiZThjYTdiYWFmMjljY2UxMDljM2IwZmU1NGM3MTg5OThlY2ZlZmZiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-71058528\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-505755150 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">40QMbx4uEb2Re2xoM5y7QokpCbUALVcgTm54X2N3</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">27pIBGGbiZrra0WKoTBCImejjaWqpombXfF8Wtf5</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-505755150\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-816306409 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 11 Jul 2025 22:05:52 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-816306409\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-985873556 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">40QMbx4uEb2Re2xoM5y7QokpCbUALVcgTm54X2N3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://localhost:8000/install/setup</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-985873556\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/install", "action_name": "install.index", "controller_action": "Corbital\\Installer\\Http\\Controllers\\InstallController@index"}, "badge": null}}