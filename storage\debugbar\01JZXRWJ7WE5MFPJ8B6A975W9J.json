{"__meta": {"id": "01JZXRWJ7WE5MFPJ8B6A975W9J", "datetime": "2025-07-11 22:14:55", "utime": **********.485449, "method": "POST", "uri": "/install/setup", "ip": "::1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.610862, "end": **********.485478, "duration": 0.8746159076690674, "duration_str": "875ms", "measures": [{"label": "Booting", "start": **********.610862, "relative_start": 0, "end": **********.930732, "relative_end": **********.930732, "duration": 0.****************, "duration_str": "320ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.930741, "relative_start": 0.*****************, "end": **********.48548, "relative_end": 2.1457672119140625e-06, "duration": 0.****************, "duration_str": "555ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.945431, "relative_start": 0.*****************, "end": **********.947719, "relative_end": **********.947719, "duration": 0.0022881031036376953, "duration_str": "2.29ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.481973, "relative_start": 0.****************, "end": **********.482322, "relative_end": **********.482322, "duration": 0.0003490447998046875, "duration_str": "349μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "35MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.28", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 8, "nb_statements": 6, "nb_visible_statements": 8, "nb_excluded_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.02821, "accumulated_duration_str": "28.21ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'xUTt0uRYmrrgY7Lnn8APemkyx7fei192BbDCEDVv' limit 1", "type": "query", "params": [], "bindings": ["xUTt0uRYmrrgY7Lnn8APemkyx7fei192BbDCEDVv"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 117}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 105}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 89}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.9548051, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 0, "width_percent": 2.41}, {"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 8, "namespace": null, "name": "vendor/corbital/installer/src/Http/Controllers/InstallController.php", "file": "C:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Http\\Controllers\\InstallController.php", "line": 258}, {"index": 9, "namespace": null, "name": "vendor/corbital/installer/src/Http/Controllers/InstallController.php", "file": "C:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Http\\Controllers\\InstallController.php", "line": 88}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.977939, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "InstallController.php:258", "source": {"index": 8, "namespace": null, "name": "vendor/corbital/installer/src/Http/Controllers/InstallController.php", "file": "C:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Http\\Controllers\\InstallController.php", "line": 258}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fcorbital%2Finstaller%2Fsrc%2FHttp%2FControllers%2FInstallController.php&line=258", "ajax": false, "filename": "InstallController.php", "line": "258"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 2.41, "width_percent": 0}, {"sql": "select * from `test_table`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/corbital/installer/src/Classes/DatabaseTest.php", "file": "C:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Classes\\DatabaseTest.php", "line": 105}, {"index": 16, "namespace": null, "name": "vendor/corbital/installer/src/Classes/DatabaseTest.php", "file": "C:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Classes\\DatabaseTest.php", "line": 104}, {"index": 17, "namespace": null, "name": "vendor/corbital/installer/src/Classes/DatabaseTest.php", "file": "C:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Classes\\DatabaseTest.php", "line": 52}, {"index": 18, "namespace": null, "name": "vendor/corbital/installer/src/Classes/DatabaseTest.php", "file": "C:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Classes\\DatabaseTest.php", "line": 100}, {"index": 19, "namespace": null, "name": "vendor/corbital/installer/src/Classes/DatabaseTest.php", "file": "C:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Classes\\DatabaseTest.php", "line": 202}], "start": **********.187844, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "DatabaseTest.php:105", "source": {"index": 13, "namespace": null, "name": "vendor/corbital/installer/src/Classes/DatabaseTest.php", "file": "C:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Classes\\DatabaseTest.php", "line": 105}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fcorbital%2Finstaller%2Fsrc%2FClasses%2FDatabaseTest.php&line=105", "ajax": false, "filename": "DatabaseTest.php", "line": "105"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 2.41, "width_percent": 1.808}, {"sql": "update `test_table` set `test_column` = 'Corbital Updated'", "type": "query", "params": [], "bindings": ["Corbital Updated"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/corbital/installer/src/Classes/DatabaseTest.php", "file": "C:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Classes\\DatabaseTest.php", "line": 120}, {"index": 14, "namespace": null, "name": "vendor/corbital/installer/src/Classes/DatabaseTest.php", "file": "C:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Classes\\DatabaseTest.php", "line": 119}, {"index": 15, "namespace": null, "name": "vendor/corbital/installer/src/Classes/DatabaseTest.php", "file": "C:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Classes\\DatabaseTest.php", "line": 52}, {"index": 16, "namespace": null, "name": "vendor/corbital/installer/src/Classes/DatabaseTest.php", "file": "C:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Classes\\DatabaseTest.php", "line": 115}, {"index": 17, "namespace": null, "name": "vendor/corbital/installer/src/Classes/DatabaseTest.php", "file": "C:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Classes\\DatabaseTest.php", "line": 203}], "start": **********.232444, "duration": 0.00275, "duration_str": "2.75ms", "memory": 0, "memory_str": null, "filename": "DatabaseTest.php:120", "source": {"index": 11, "namespace": null, "name": "vendor/corbital/installer/src/Classes/DatabaseTest.php", "file": "C:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Classes\\DatabaseTest.php", "line": 120}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fcorbital%2Finstaller%2Fsrc%2FClasses%2FDatabaseTest.php&line=120", "ajax": false, "filename": "DatabaseTest.php", "line": "120"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 4.218, "width_percent": 9.748}, {"sql": "delete from `test_table`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/corbital/installer/src/Classes/DatabaseTest.php", "file": "C:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Classes\\DatabaseTest.php", "line": 135}, {"index": 14, "namespace": null, "name": "vendor/corbital/installer/src/Classes/DatabaseTest.php", "file": "C:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Classes\\DatabaseTest.php", "line": 134}, {"index": 15, "namespace": null, "name": "vendor/corbital/installer/src/Classes/DatabaseTest.php", "file": "C:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Classes\\DatabaseTest.php", "line": 52}, {"index": 16, "namespace": null, "name": "vendor/corbital/installer/src/Classes/DatabaseTest.php", "file": "C:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Classes\\DatabaseTest.php", "line": 130}, {"index": 17, "namespace": null, "name": "vendor/corbital/installer/src/Classes/DatabaseTest.php", "file": "C:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Classes\\DatabaseTest.php", "line": 204}], "start": **********.261121, "duration": 0.0016799999999999999, "duration_str": "1.68ms", "memory": 0, "memory_str": null, "filename": "DatabaseTest.php:135", "source": {"index": 11, "namespace": null, "name": "vendor/corbital/installer/src/Classes/DatabaseTest.php", "file": "C:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Classes\\DatabaseTest.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fcorbital%2Finstaller%2Fsrc%2FClasses%2FDatabaseTest.php&line=135", "ajax": false, "filename": "DatabaseTest.php", "line": "135"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 13.967, "width_percent": 5.955}, {"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "vendor/corbital/installer/src/Http/Controllers/InstallController.php", "file": "C:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Http\\Controllers\\InstallController.php", "line": 115}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.447791, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "InstallController.php:115", "source": {"index": 9, "namespace": null, "name": "vendor/corbital/installer/src/Http/Controllers/InstallController.php", "file": "C:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Http\\Controllers\\InstallController.php", "line": 115}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fcorbital%2Finstaller%2Fsrc%2FHttp%2FControllers%2FInstallController.php&line=115", "ajax": false, "filename": "InstallController.php", "line": "115"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 19.922, "width_percent": 0}, {"sql": "SET FOREIGN_KEY_CHECKS=0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/corbital/installer/src/Http/Controllers/InstallController.php", "file": "C:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Http\\Controllers\\InstallController.php", "line": 116}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.447886, "duration": 0.012320000000000001, "duration_str": "12.32ms", "memory": 0, "memory_str": null, "filename": "InstallController.php:116", "source": {"index": 11, "namespace": null, "name": "vendor/corbital/installer/src/Http/Controllers/InstallController.php", "file": "C:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Http\\Controllers\\InstallController.php", "line": 116}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fcorbital%2Finstaller%2Fsrc%2FHttp%2FControllers%2FInstallController.php&line=116", "ajax": false, "filename": "InstallController.php", "line": "116"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 19.922, "width_percent": 43.672}, {"sql": "select exists (select 1 from information_schema.tables where table_schema = 'whatsmark_non_saas' and table_name = 'sessions' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/corbital/installer/src/Http/Controllers/InstallController.php", "file": "C:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Http\\Controllers\\InstallController.php", "line": 118}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.4662738, "duration": 0.01027, "duration_str": "10.27ms", "memory": 0, "memory_str": null, "filename": "InstallController.php:118", "source": {"index": 13, "namespace": null, "name": "vendor/corbital/installer/src/Http/Controllers/InstallController.php", "file": "C:\\laragon\\www\\whats\\vendor\\corbital\\installer\\src\\Http\\Controllers\\InstallController.php", "line": 118}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fcorbital%2Finstaller%2Fsrc%2FHttp%2FControllers%2FInstallController.php&line=118", "ajax": false, "filename": "InstallController.php", "line": "118"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 63.594, "width_percent": 36.406}]}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "302 Found", "full_url": "http://localhost:8000/install/setup", "action_name": "install.setup.store", "controller_action": "Corbital\\Installer\\Http\\Controllers\\InstallController@setupStore", "uri": "POST install/setup", "controller": "Corbital\\Installer\\Http\\Controllers\\InstallController@setupStore<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fcorbital%2Finstaller%2Fsrc%2FHttp%2FControllers%2FInstallController.php&line=84\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "install", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fcorbital%2Finstaller%2Fsrc%2FHttp%2FControllers%2FInstallController.php&line=84\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/corbital/installer/src/Http/Controllers/InstallController.php:84-159</a>", "middleware": "web, Corbital\\Installer\\Http\\Middleware\\CanInstall", "duration": "880ms", "peak_memory": "36MB", "response": "Redirect to http://localhost:8000/install/license", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-948281335 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-948281335\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">i9Ht9dlB3HMUFMBIlxPaWRpNTIMIOJRkfY3Zrqqq</span>\"\n  \"<span class=sf-dump-key>app_url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  \"<span class=sf-dump-key>app_name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Laravel</span>\"\n  \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str title=\"3 characters\">105</span>\"\n  \"<span class=sf-dump-key>database_hostname</span>\" => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  \"<span class=sf-dump-key>database_port</span>\" => \"<span class=sf-dump-str title=\"4 characters\">3306</span>\"\n  \"<span class=sf-dump-key>database_name</span>\" => \"<span class=sf-dump-str title=\"18 characters\">whatsmark_non_saas</span>\"\n  \"<span class=sf-dump-key>database_username</span>\" => \"<span class=sf-dump-str title=\"4 characters\">root</span>\"\n  \"<span class=sf-dump-key>database_password</span>\" => \"<span class=sf-dump-str title=\"4 characters\">root</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-193949622 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">240</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://localhost:8000/install/setup</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImYwQllGL2lDR0dab3owRytCQ0xleUE9PSIsInZhbHVlIjoidys5Y0NBWWZEZ0YxL2EvZjBieUFyd0V5S0tpSThBanlXaXlHbU14RkYzeTFMa0ZLTmV4ZUw1WHMwWU83WlRwRzJuZ3NMSnJrQStkQU5NczJWd0pSVTBjV2hxWWFXT20rM3k4WitEbjNTaEhrVXlwL3JCQitsQ1ZNTzZ5bE8rblgiLCJtYWMiOiJjODRlODgyOTdkNzEyZjcxNDQ5MWE0NzQ2MTk5YTM2NjEwNGY4ZjhhOGI2YTVmY2RlNTU3MjA4MWVjYmUwZTExIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkxPWWhQL3pJV2pGdEhEaWtmamx2VUE9PSIsInZhbHVlIjoic1FUTHNKTHNMcHZjM0N4RGVSYndvSzVxY1RiK1Y1ZjVJbUFxSlpqSjBqa3Zjd05ZL0ppalV3L1NUNU1scG5YeEdpWWIyN2w4SnpBa0ovNXBvc0NjdWxsamQyU2ZZeFRHOWZiclNmOFFlNjl4N1NxMDFwOWJlMmk4MXFHMzd0aE0iLCJtYWMiOiI3OTY0MWU3ZmQ2NTNmOWQxZWEwZjJjODQzMDgwNTY2M2YwZjQwZmViMmRiMTlhZTVlNWRjODQwODlmNWJmYTAwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-193949622\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2117733721 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">i9Ht9dlB3HMUFMBIlxPaWRpNTIMIOJRkfY3Zrqqq</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xUTt0uRYmrrgY7Lnn8APemkyx7fei192BbDCEDVv</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2117733721\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-356776015 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 11 Jul 2025 22:14:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost:8000/install/license</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-356776015\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-25778514 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">i9Ht9dlB3HMUFMBIlxPaWRpNTIMIOJRkfY3Zrqqq</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://localhost:8000/install</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str title=\"3 characters\">105</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-25778514\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "http://localhost:8000/install/setup", "action_name": "install.setup.store", "controller_action": "Corbital\\Installer\\Http\\Controllers\\InstallController@setupStore"}, "badge": "302 Found"}}