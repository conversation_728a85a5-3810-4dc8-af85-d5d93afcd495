<?php return array (
  'barryvdh/laravel-debugbar' => 
  array (
    'aliases' => 
    array (
      'Debugbar' => 'Barryvdh\\Debugbar\\Facades\\Debugbar',
    ),
    'providers' => 
    array (
      0 => 'Barryvdh\\Debugbar\\ServiceProvider',
    ),
  ),
  'barryvdh/laravel-ide-helper' => 
  array (
    'providers' => 
    array (
      0 => 'Barryvdh\\LaravelIdeHelper\\IdeHelperServiceProvider',
    ),
  ),
  'blade-ui-kit/blade-heroicons' => 
  array (
    'providers' => 
    array (
      0 => 'BladeUI\\Heroicons\\BladeHeroiconsServiceProvider',
    ),
  ),
  'blade-ui-kit/blade-icons' => 
  array (
    'providers' => 
    array (
      0 => 'BladeUI\\Icons\\BladeIconsServiceProvider',
    ),
  ),
  'brunocfalcao/blade-feather-icons' => 
  array (
    'providers' => 
    array (
      0 => 'Brunocfalcao\\BladeFeatherIcons\\BladeFeatherIconsServiceProvider',
    ),
  ),
  'codeat3/blade-carbon-icons' => 
  array (
    'providers' => 
    array (
      0 => 'Codeat3\\BladeCarbonIcons\\BladeCarbonIconsServiceProvider',
    ),
  ),
  'corbital/installer' => 
  array (
    'providers' => 
    array (
      0 => 'Corbital\\Installer\\Providers\\InstallerServiceProvider',
    ),
    'aliases' => 
    array (
      'Installer' => 'Corbital\\Installer\\Facades\\Installer',
    ),
  ),
  'knuckleswtf/scribe' => 
  array (
    'providers' => 
    array (
      0 => 'Knuckles\\Scribe\\ScribeServiceProvider',
    ),
  ),
  'laravel/breeze' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Breeze\\BreezeServiceProvider',
    ),
  ),
  'laravel/tinker' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Tinker\\TinkerServiceProvider',
    ),
  ),
  'laravolt/avatar' => 
  array (
    'aliases' => 
    array (
      'Avatar' => 'Laravolt\\Avatar\\Facade',
    ),
    'providers' => 
    array (
      0 => 'Laravolt\\Avatar\\ServiceProvider',
    ),
  ),
  'livewire/livewire' => 
  array (
    'aliases' => 
    array (
      'Livewire' => 'Livewire\\Livewire',
    ),
    'providers' => 
    array (
      0 => 'Livewire\\LivewireServiceProvider',
    ),
  ),
  'nesbot/carbon' => 
  array (
    'providers' => 
    array (
      0 => 'Carbon\\Laravel\\ServiceProvider',
    ),
  ),
  'nunomaduro/collision' => 
  array (
    'providers' => 
    array (
      0 => 'NunoMaduro\\Collision\\Adapters\\Laravel\\CollisionServiceProvider',
    ),
  ),
  'nunomaduro/termwind' => 
  array (
    'providers' => 
    array (
      0 => 'Termwind\\Laravel\\TermwindServiceProvider',
    ),
  ),
  'pestphp/pest-plugin-laravel' => 
  array (
    'providers' => 
    array (
      0 => 'Pest\\Laravel\\PestServiceProvider',
    ),
  ),
  'pharaonic/livewire-select2' => 
  array (
    'providers' => 
    array (
      0 => 'Pharaonic\\Livewire\\Select2\\Select2ServiceProvider',
    ),
  ),
  'power-components/livewire-powergrid' => 
  array (
    'providers' => 
    array (
      0 => 'PowerComponents\\LivewirePowerGrid\\Providers\\PowerGridServiceProvider',
    ),
  ),
  'spatie/laravel-permission' => 
  array (
    'providers' => 
    array (
      0 => 'Spatie\\Permission\\PermissionServiceProvider',
    ),
  ),
  'spatie/laravel-settings' => 
  array (
    'providers' => 
    array (
      0 => 'Spatie\\LaravelSettings\\LaravelSettingsServiceProvider',
    ),
  ),
  'timehunter/laravel-google-recaptcha-v3' => 
  array (
    'aliases' => 
    array (
      'GoogleReCaptchaV3' => 'TimeHunter\\LaravelGoogleReCaptchaV3\\Facades\\GoogleReCaptchaV3',
    ),
    'providers' => 
    array (
      0 => 'TimeHunter\\LaravelGoogleReCaptchaV3\\Providers\\GoogleReCaptchaV3ServiceProvider',
    ),
  ),
);